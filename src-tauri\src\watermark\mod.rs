use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// 文字水印
#[tauri::command]
pub async fn add_text_watermark(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    text: String,
    position: String,
    font_size: i32,
    font_color: String,
    opacity: f32,
    custom_x: Option<i32>,
    custom_y: Option<i32>,
) -> Result<(), String> {
    println!("add_text_watermark called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  text: {}", text);
    println!("  position: {}", position);
    println!("  font_size: {}", font_size);
    println!("  font_color: {}", font_color);
    println!("  opacity: {}", opacity);
    println!("  custom_x: {:?}", custom_x);
    println!("  custom_y: {:?}", custom_y);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    // 计算水印位置
    let (x, y) = match position.as_str() {
        "左上" => (10, 10),
        "右上" => (-1, 10), // 使用-1表示右对齐
        "左下" => (10, -1), // 使用-1表示下对齐
        "右下" => (-1, -1), // 使用-1表示右下对齐
        "自定义" => {
            let x = custom_x.unwrap_or(10);
            let y = custom_y.unwrap_or(10);
            (x, y)
        }
        _ => (10, 10), // 默认左上
    };

    // 构建drawtext滤镜参数
    let x_param = if x == -1 { "w-tw-10" } else { &x.to_string() };
    let y_param = if y == -1 { "h-th-10" } else { &y.to_string() };

    // Windows 下指定字体文件，防止 fontconfig 错误和崩溃
    let fontfile = "C:\\Windows\\Fonts\\msyh.ttc";
    let fontcolor_with_opacity = if font_color.contains("@") {
        font_color.clone()
    } else {
        format!("{}@{}", font_color, opacity)
    };
    let drawtext_filter = format!(
        "drawtext=fontfile='{}':text='{}':fontsize={}:fontcolor={}:x={}:y={}:box=1:boxcolor=black@0.5:boxborderw=5",
        fontfile, text, font_size, fontcolor_with_opacity, x_param, y_param
    );

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("应用文字水印: {}", text);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &drawtext_filter,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .print_command()
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("WATERMARK_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("WATERMARK_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 文字水印处理完成！");
    window
        .emit("WATERMARK_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 图片水印
#[tauri::command]
pub async fn add_image_watermark(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    watermark_path: String,
    position: String,
    width: i32,
    opacity: f32,
    custom_x: Option<i32>,
    custom_y: Option<i32>,
) -> Result<(), String> {
    println!("add_image_watermark called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  watermark_path: {}", watermark_path);
    println!("  position: {}", position);
    println!("  width: {}", width);
    println!("  opacity: {}", opacity);
    println!("  custom_x: {:?}", custom_x);
    println!("  custom_y: {:?}", custom_y);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 检查水印文件是否存在
    if !Path::new(&watermark_path).exists() {
        return Err("水印文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    // 计算水印位置
    let (x, y) = match position.as_str() {
        "左上" => (10, 10),
        "右上" => (-1, 10), // 使用-1表示右对齐
        "左下" => (10, -1), // 使用-1表示下对齐
        "右下" => (-1, -1), // 使用-1表示右下对齐
        "自定义" => {
            let x = custom_x.unwrap_or(10);
            let y = custom_y.unwrap_or(10);
            (x, y)
        }
        _ => (10, 10), // 默认左上
    };

    // 构建overlay滤镜参数
    let x_param = if x == -1 { "W-w-10" } else { &x.to_string() };
    let y_param = if y == -1 { "H-h-10" } else { &y.to_string() };

    // 获取视频信息以确定时长
    let video_info = get_video_info_internal(&input_path).await?;
    let video_duration = video_info.duration;

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("应用图片水印: {}", watermark_path);
    println!("视频时长: {:.2}秒", video_duration);

    // 修复水印只显示前几秒的问题：使用-stream_loop让水印图片循环显示整个视频时长
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-stream_loop", "-1",  // 无限循环水印图片
            "-i",
            &watermark_path,
            "-filter_complex",
            &format!(
                "[1:v]scale={width}:-1,format=rgba,colorchannelmixer=aa={opacity}[watermark];[0:v][watermark]overlay={x}:{y},format=yuv420p",
                width = width,
                opacity = opacity,
                x = x_param,
                y = y_param
            ),
            "-t", &video_duration.to_string(),  // 限制输出时长为原视频时长
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .print_command()
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("WATERMARK_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("WATERMARK_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 图片水印处理完成！");
    window
        .emit("WATERMARK_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 视频水印
#[tauri::command]
pub async fn add_video_watermark(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    watermark_path: String,
    position: String,
    width: i32,
    opacity: f32,
    custom_x: Option<i32>,
    custom_y: Option<i32>,
) -> Result<(), String> {
    println!("add_video_watermark called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  watermark_path: {}", watermark_path);
    println!("  position: {}", position);
    println!("  width: {}", width);
    println!("  opacity: {}", opacity);
    println!("  custom_x: {:?}", custom_x);
    println!("  custom_y: {:?}", custom_y);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 检查水印文件是否存在
    if !Path::new(&watermark_path).exists() {
        return Err("水印文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    // 计算水印位置
    let (x, y) = match position.as_str() {
        "左上" => (10, 10),
        "右上" => (-1, 10), // 使用-1表示右对齐
        "左下" => (10, -1), // 使用-1表示下对齐
        "右下" => (-1, -1), // 使用-1表示右下对齐
        "自定义" => {
            let x = custom_x.unwrap_or(10);
            let y = custom_y.unwrap_or(10);
            (x, y)
        }
        _ => (10, 10), // 默认左上
    };

    // 构建overlay滤镜参数
    let x_param = if x == -1 { "W-w-10" } else { &x.to_string() };
    let y_param = if y == -1 { "H-h-10" } else { &y.to_string() };

    let filter_complex = format!(
        "[1:v]scale={width}:-1,format=rgba,colorchannelmixer=aa={opacity},fps=fps=30[watermark];[0:v][watermark]overlay={x}:{y},format=yuv420p",
        width = width,
        opacity = opacity,
        x = x_param,
        y = y_param
    );

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("应用视频水印: {}", watermark_path);

    let duration_arg = format!("{}", video_info.duration);
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-stream_loop",
            "-1",
            "-i",
            &watermark_path,
            "-t",
            &duration_arg,
            "-filter_complex",
            &filter_complex,
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("WATERMARK_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("WATERMARK_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频水印处理完成！");
    window
        .emit("WATERMARK_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}
